import httpx
import io
import asyncio
from typing import List, Optional
from core.rag.rag_service import RAGServiceBase, FileUploadResult
from core.services.database.schemas.user_uploaded_files import FileStatus
from core.config.app_config import config
from core.config.app_logger import logger
from pydantic import BaseModel


class RagFlowError(Exception):
    """RagFlow服务相关错误的基类"""
    pass


class RagFlowAuthenticationError(RagFlowError):
    """RagFlow认证错误"""
    pass


class RagFlowNetworkError(RagFlowError):
    """RagFlow网络连接错误"""
    pass


class RagFlowAPIError(RagFlowError):
    """RagFlow API错误"""
    def __init__(self, message: str, code: int = None, response_data: dict = None):
        super().__init__(message)
        self.code = code
        self.response_data = response_data

class RagFlowConfig(BaseModel):
    """RagFlow RAG服务配置"""

    base_url: str = ""  # RagFlow服务地址
    api_key: str  # RagFlow API密钥
    dataset_id: str  # 默认数据集ID
    timeout: int = 120  # 请求超时时间(秒)
    chunk_method: str = "naive"  # 默认文档解析方法
    embedding_model: str = ""  # 默认嵌入模型


class RagFlowRAGService(RAGServiceBase):
    """
    RagFlow RAG服务实现类

    实现与RagFlow API的集成，提供文件上传、解析、状态查询、删除和检索等功能。
    支持多种文档格式的处理和向量化检索。
    """

    def __init__(self, rag_server: str = "ragflow", ragflow_config: Optional[RagFlowConfig] = None):
        """
        初始化RagFlow RAG服务

        Args:
            rag_server: RAG服务标识符
            ragflow_config: RagFlow配置，如果为None则从全局配置加载
        """
        super().__init__(rag_server)

        # 加载RagFlow配置
        if ragflow_config:
            self.ragflow_config = ragflow_config
        else:

            services = config.rag_services["services"]
            for service_config in services:
                if service_config["name"] == "ragflow":
                    self.ragflow_config = service_config
            if not self.ragflow_config:
                raise ValueError("RagFlow配置未找到，请在配置文件中添加ragflow配置项")

        # 设置HTTP客户端默认配置
        self.headers = {
            "Authorization": f"Bearer {self.ragflow_config.api_key}",
            "User-Agent": "RagFlowRAGService/1.0"
        }
        self.timeout = self.ragflow_config.timeout

        logger.info(f"RagFlow RAG服务初始化完成: base_url={self.ragflow_config.base_url}, dataset_id={self.ragflow_config.dataset_id}")

    def _get_api_url(self, endpoint: str) -> str:
        """构建完整的API URL"""
        base_url = self.ragflow_config.base_url.rstrip('/')
        endpoint = endpoint.lstrip('/')
        return f"{base_url}/api/v1/{endpoint}"

    def _map_ragflow_status_to_file_status(self, ragflow_status: str) -> FileStatus:
        """
        将RagFlow的文档状态映射到内部FileStatus枚举

        Args:
            ragflow_status: RagFlow返回的状态字符串

        Returns:
            FileStatus: 映射后的内部状态
        """
        # RagFlow状态映射 (基于API文档推断)
        status_mapping = {
            "0": FileStatus.UPLOADING,    # 上传中
            "1": FileStatus.READY,        # 已完成/可用
            "2": FileStatus.PARSING,      # 解析中
            "3": FileStatus.ERROR,        # 错误
            "uploading": FileStatus.UPLOADING,
            "parsing": FileStatus.PARSING,
            "ready": FileStatus.READY,
            "completed": FileStatus.READY,
            "error": FileStatus.ERROR,
            "failed": FileStatus.ERROR
        }

        return status_mapping.get(str(ragflow_status).lower(), FileStatus.ERROR)

    def _handle_http_error(self, response: httpx.Response, operation: str) -> None:
        """
        处理HTTP错误响应

        Args:
            response: HTTP响应对象
            operation: 操作名称（用于日志）

        Raises:
            RagFlowAuthenticationError: 认证错误
            RagFlowAPIError: API错误
        """
        if response.status_code == 401:
            raise RagFlowAuthenticationError("RagFlow认证失败，请检查API密钥")
        elif response.status_code == 403:
            raise RagFlowAuthenticationError("RagFlow权限不足，请检查API密钥权限")
        elif response.status_code == 404:
            raise RagFlowAPIError(f"RagFlow资源未找到: {operation}")
        elif response.status_code >= 500:
            raise RagFlowAPIError(f"RagFlow服务器错误: {operation}, HTTP {response.status_code}")
        else:
            try:
                error_data = response.json()
                error_msg = error_data.get("message", f"HTTP {response.status_code}")
                error_code = error_data.get("code")
                raise RagFlowAPIError(f"RagFlow API错误: {operation} - {error_msg}", error_code, error_data)
            except ValueError:
                # 响应不是JSON格式
                raise RagFlowAPIError(f"RagFlow API错误: {operation}, HTTP {response.status_code}")

    async def _make_async_request(self, method: str, url: str, **kwargs) -> httpx.Response:
        """
        执行异步HTTP请求的通用方法

        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数

        Returns:
            httpx.Response: HTTP响应对象

        Raises:
            RagFlowNetworkError: 网络错误
            RagFlowError: 其他错误
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.request(method, url, **kwargs)
                return response
        except httpx.TimeoutException as e:
            raise RagFlowNetworkError(f"请求超时: {url}")
        except httpx.ConnectError as e:
            raise RagFlowNetworkError(f"连接失败: {url} - {str(e)}")
        except httpx.RequestError as e:
            raise RagFlowNetworkError(f"网络请求错误: {url} - {str(e)}")
        except Exception as e:
            raise RagFlowError(f"请求异常: {url} - {str(e)}")

    def _run_async_request(self, method: str, url: str, **kwargs) -> httpx.Response:
        """
        在同步方法中运行异步请求

        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数

        Returns:
            httpx.Response: HTTP响应对象
        """
        async def _request():
            return await self._make_async_request(method, url, **kwargs)

        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(_request())
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            return asyncio.run(_request())

    async def upload_file_to_rag_service(
        self,
        *,
        file_content: bytes,
        file_name: str,
        file_size: int,
        file_hash: str,
        file_type: str,
    ) -> FileUploadResult:
        """
        上传文件到RagFlow服务

        Args:
            file_content: 文件二进制内容
            file_name: 文件名
            file_size: 文件大小（字节）
            file_hash: 文件SHA256哈希值
            file_type: 文件MIME类型

        Returns:
            FileUploadResult: 上传结果，包含成功状态、文件ID和状态
        """
        try:
            # 构建上传URL
            url = self._get_api_url(f"datasets/{self.ragflow_config.dataset_id}/documents")

            # 准备multipart/form-data
            files = {
                'file': (file_name, io.BytesIO(file_content), file_type or 'application/octet-stream')
            }

            # 设置请求头（不包含Content-Type，让httpx自动设置multipart边界）
            headers = {
                "Authorization": f"Bearer {self.ragflow_config.api_key}",
                "User-Agent": "RagFlowRAGService/1.0"
            }

            logger.info(f"开始上传文件到RagFlow: {file_name} ({file_size} bytes)")

            # 使用新的错误处理方法
            response = await self._make_async_request("POST", url, files=files, headers=headers)

            # 检查HTTP状态码
            if response.status_code == 200:
                response_data = response.json()

                # 解析响应数据
                if response_data.get("code") == 0:
                    # 上传成功
                    data = response_data.get("data", {})

                    # 从响应中提取文档ID
                    # 根据API文档，上传成功后应该返回文档信息
                    if isinstance(data, list) and len(data) > 0:
                        doc_info = data[0]
                        doc_id = doc_info.get("id")
                    elif isinstance(data, dict):
                        doc_id = data.get("id") or data.get("document_id")
                    else:
                        # 如果响应格式不符合预期，使用文件哈希作为临时ID
                        doc_id = file_hash

                    logger.info(f"文件上传成功: {file_name}, document_id: {doc_id}")

                    return FileUploadResult(
                        success=True,
                        rag_file_id=doc_id,
                        status=FileStatus.UPLOADING  # 上传成功，等待解析
                    )
                else:
                    # API返回错误
                    error_msg = response_data.get("message", "未知错误")
                    error_code = response_data.get("code")
                    logger.error(f"RagFlow API返回错误: {error_msg} (code: {error_code})")

                    return FileUploadResult(
                        success=False,
                        rag_file_id="",
                        status=FileStatus.ERROR
                    )
            else:
                # HTTP错误
                self._handle_http_error(response, f"上传文件 {file_name}")

        except (RagFlowAuthenticationError, RagFlowNetworkError, RagFlowAPIError) as e:
            logger.error(f"文件上传失败: {file_name}, 错误: {str(e)}")
            return FileUploadResult(
                success=False,
                rag_file_id="",
                status=FileStatus.ERROR
            )
        except Exception as e:
            logger.error(f"文件上传异常: {file_name}, 错误: {str(e)}", exc_info=True)
            return FileUploadResult(
                success=False,
                rag_file_id="",
                status=FileStatus.ERROR
            )

    def parse_file(self, file_id: str) -> bool:
        """
        触发RagFlow文档解析

        Args:
            file_id: RagFlow文档ID

        Returns:
            bool: 解析请求是否成功提交
        """
        try:
            # 构建解析URL
            url = self._get_api_url(f"datasets/{self.ragflow_config.dataset_id}/chunks")

            # 准备请求数据
            payload = {
                "document_ids": [file_id]
            }

            headers = {
                **self.headers,
                "Content-Type": "application/json"
            }

            logger.info(f"开始解析文档: {file_id}")

            # 使用新的错误处理方法
            response = self._run_async_request("POST", url, json=payload, headers=headers)

            # 检查响应
            if response.status_code == 200:
                response_data = response.json()

                if response_data.get("code") == 0:
                    logger.info(f"文档解析请求提交成功: {file_id}")
                    return True
                else:
                    error_msg = response_data.get("message", "未知错误")
                    error_code = response_data.get("code")
                    logger.error(f"文档解析请求失败: {file_id}, 错误: {error_msg} (code: {error_code})")
                    return False
            else:
                self._handle_http_error(response, f"解析文档 {file_id}")
                return False

        except (RagFlowAuthenticationError, RagFlowNetworkError, RagFlowAPIError) as e:
            logger.error(f"文档解析请求失败: {file_id}, 错误: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"文档解析请求异常: {file_id}, 错误: {str(e)}", exc_info=True)
            return False

    def get_file_status(self, file_id: str) -> dict:
        """
        查询文件处理状态

        Args:
            file_id: RagFlow文档ID

        Returns:
            dict: 包含状态信息的字典，格式为:
                {
                    "status": FileStatus,
                    "message": str,
                    "progress": float,  # 0.0-1.0
                    "error_message": str or None
                }
        """
        try:
            # 构建查询URL - 获取文档列表并过滤指定ID
            url = self._get_api_url(f"datasets/{self.ragflow_config.dataset_id}/documents")

            headers = {
                **self.headers
            }

            logger.debug(f"查询文档状态: {file_id}")

            # 使用同步请求
            import asyncio

            async def _status_request():
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    # 获取数据集中的所有文档
                    response = await client.get(url, headers=headers)
                    return response

            # 在事件循环中运行异步请求
            try:
                loop = asyncio.get_event_loop()
                response = loop.run_until_complete(_status_request())
            except RuntimeError:
                response = asyncio.run(_status_request())

            if response.status_code == 200:
                response_data = response.json()

                if response_data.get("code") == 0:
                    documents = response_data.get("data", [])

                    # 查找指定ID的文档
                    target_doc = None
                    for doc in documents:
                        if doc.get("id") == file_id:
                            target_doc = doc
                            break

                    if target_doc:
                        # 解析文档状态
                        doc_status = target_doc.get("status", "0")
                        chunk_count = target_doc.get("chunk_count", 0)

                        # 映射状态
                        file_status = self._map_ragflow_status_to_file_status(doc_status)

                        # 计算进度（基于chunk数量）
                        progress = 1.0 if file_status == FileStatus.READY and chunk_count > 0 else 0.0
                        if file_status == FileStatus.PARSING:
                            progress = 0.5  # 解析中设为50%

                        # 构建状态消息
                        status_messages = {
                            FileStatus.UPLOADING: "文件上传中",
                            FileStatus.PARSING: "文档解析中",
                            FileStatus.READY: f"文档已就绪，共{chunk_count}个文档块",
                            FileStatus.ERROR: "文档处理失败"
                        }

                        result = {
                            "status": file_status,
                            "message": status_messages.get(file_status, "未知状态"),
                            "progress": progress,
                            "error_message": None,
                            "chunk_count": chunk_count,
                            "raw_status": doc_status
                        }

                        logger.debug(f"文档状态查询成功: {file_id}, 状态: {file_status.value}")
                        return result
                    else:
                        # 文档未找到
                        logger.warning(f"文档未找到: {file_id}")
                        return {
                            "status": FileStatus.ERROR,
                            "message": "文档未找到",
                            "progress": 0.0,
                            "error_message": "指定的文档ID不存在",
                            "chunk_count": 0,
                            "raw_status": None
                        }
                else:
                    error_msg = response_data.get("message", "未知错误")
                    logger.error(f"查询文档状态失败: {file_id}, 错误: {error_msg}")
                    return {
                        "status": FileStatus.ERROR,
                        "message": "查询失败",
                        "progress": 0.0,
                        "error_message": error_msg,
                        "chunk_count": 0,
                        "raw_status": None
                    }
            else:
                logger.error(f"查询文档状态失败，HTTP状态码: {response.status_code}")
                return {
                    "status": FileStatus.ERROR,
                    "message": "网络请求失败",
                    "progress": 0.0,
                    "error_message": f"HTTP {response.status_code}",
                    "chunk_count": 0,
                    "raw_status": None
                }

        except Exception as e:
            logger.error(f"查询文档状态异常: {file_id}, 错误: {str(e)}", exc_info=True)
            return {
                "status": FileStatus.ERROR,
                "message": "查询异常",
                "progress": 0.0,
                "error_message": str(e),
                "chunk_count": 0,
                "raw_status": None
            }

    def delete_file_from_rag_service(self, user_id: str, file_ids: List[str]) -> bool:
        """
        从RagFlow服务删除文件

        Args:
            user_id: 用户ID（用于日志记录和权限验证）
            file_ids: 要删除的RagFlow文档ID列表

        Returns:
            bool: 删除操作是否成功
        """
        if not file_ids:
            logger.warning(f"用户 {user_id} 尝试删除空文件列表")
            return True  # 空列表视为成功

        try:
            # 构建删除URL
            url = self._get_api_url(f"datasets/{self.ragflow_config.dataset_id}/documents")

            # 准备请求数据
            payload = {
                "ids": file_ids
            }

            headers = {
                **self.headers,
                "Content-Type": "application/json"
            }

            logger.info(f"用户 {user_id} 开始删除文档: {file_ids}")

            # 使用同步请求
            import asyncio

            async def _delete_request():
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.delete(url, json=payload, headers=headers)
                    return response

            # 在事件循环中运行异步请求
            try:
                loop = asyncio.get_event_loop()
                response = loop.run_until_complete(_delete_request())
            except RuntimeError:
                response = asyncio.run(_delete_request())

            # 检查响应
            if response.status_code == 200:
                response_data = response.json()

                if response_data.get("code") == 0:
                    logger.info(f"用户 {user_id} 文档删除成功: {file_ids}")
                    return True
                else:
                    error_msg = response_data.get("message", "未知错误")
                    logger.error(f"用户 {user_id} 文档删除失败: {file_ids}, 错误: {error_msg}")
                    return False
            else:
                logger.error(f"用户 {user_id} 文档删除失败，HTTP状态码: {response.status_code}, 响应: {response.text}")
                return False

        except Exception as e:
            logger.error(f"用户 {user_id} 文档删除异常: {file_ids}, 错误: {str(e)}", exc_info=True)
            return False

    def retrieve_from_documents(
        self, query: str, user_id: str, file_ids: list[str]
    ) -> list[dict]:
        """
        从指定文档中检索相关内容

        Args:
            query: 查询字符串
            user_id: 用户ID（用于权限验证和日志记录）
            file_ids: 要检索的文档ID列表

        Returns:
            list[dict]: 检索结果列表，每个元素包含:
                {
                    "content": str,           # 文档块内容
                    "score": float,           # 相似度分数
                    "document_id": str,       # 文档ID
                    "chunk_id": str,          # 文档块ID
                    "metadata": dict          # 元数据信息
                }
        """
        if not query.strip():
            logger.warning(f"用户 {user_id} 提供了空查询字符串")
            return []

        if not file_ids:
            logger.warning(f"用户 {user_id} 提供了空文档ID列表")
            return []

        try:
            # 构建检索URL
            url = self._get_api_url(f"datasets/{self.ragflow_config.dataset_id}/chunks")

            # 准备查询参数
            params = {
                "query": query,
                "document_ids": ",".join(file_ids),  # 将文档ID列表转为逗号分隔的字符串
                "limit": 10,  # 限制返回结果数量
                "similarity_threshold": 0.1  # 相似度阈值
            }

            headers = {
                **self.headers
            }

            logger.info(f"用户 {user_id} 开始检索文档: query='{query}', file_ids={file_ids}")

            # 使用同步请求
            import asyncio

            async def _retrieve_request():
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.get(url, params=params, headers=headers)
                    return response

            # 在事件循环中运行异步请求
            try:
                loop = asyncio.get_event_loop()
                response = loop.run_until_complete(_retrieve_request())
            except RuntimeError:
                response = asyncio.run(_retrieve_request())

            # 检查响应
            if response.status_code == 200:
                response_data = response.json()

                if response_data.get("code") == 0:
                    chunks = response_data.get("data", [])

                    # 格式化检索结果
                    results = []
                    for chunk in chunks:
                        result_item = {
                            "content": chunk.get("content", ""),
                            "score": chunk.get("similarity", 0.0),
                            "document_id": chunk.get("document_id", ""),
                            "chunk_id": chunk.get("id", ""),
                            "metadata": {
                                "document_name": chunk.get("document_name", ""),
                                "page_number": chunk.get("page_number"),
                                "position": chunk.get("position", {}),
                                "create_time": chunk.get("create_time"),
                                "update_time": chunk.get("update_time")
                            }
                        }
                        results.append(result_item)

                    logger.info(f"用户 {user_id} 检索完成: 查询='{query}', 返回{len(results)}个结果")
                    return results
                else:
                    error_msg = response_data.get("message", "未知错误")
                    logger.error(f"用户 {user_id} 文档检索失败: {error_msg}")
                    return []
            else:
                logger.error(f"用户 {user_id} 文档检索失败，HTTP状态码: {response.status_code}, 响应: {response.text}")
                return []

        except Exception as e:
            logger.error(f"用户 {user_id} 文档检索异常: query='{query}', 错误: {str(e)}", exc_info=True)
            return []
