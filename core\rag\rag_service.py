from abc import ABC, abstractmethod
from dataclasses import dataclass
import hashlib
import mimetypes
from typing import List
from core.services.database import db_manager
from core.services.database.crud.user_uploaded_files import user_uploaded_files_crud
from core.services.database.schemas.user_uploaded_files import (
    FileStatus,
    UserUploadedFilesCreate,
)


@dataclass
class FileUploadResult:
    success: bool
    rag_file_id: str
    status: FileStatus


class RAGServiceBase(ABC):
    """
    我们将 RAGFlow 的所有功能封装在一个独立的的RAGFlowService中。
    智能体项目的其他部分（如对话管理器、业务逻辑层）不直接调用 RAGFlow 的原生 API，而是通过这个统一的、语义清晰的服务层进行交互。
    RAGFlowService作为适配器，负责转换智能体项目的内部请求和 RAGFlow 的原生 API。未来如果 RAGFlow 的 API 发生变更，我们只需要修改RAGFlowService内部的实现，而无需改动智能体的上层业务代码。
    如果未来需要替换为其他 RAG 框架，我们只需重新实现一个遵循相同接口的服务即可。
    """

    def __init__(self, rag_server: str):
        self.rag_server = rag_server

    async def _save_file(
        self,
        *,
        user_id: str,
        file_id: str,
        file_name: str,
        file_size: int,
        file_hash: str,
        file_type: str,
        status: FileStatus,
    ):
        """
        保存文件信息到数据库

        """
        try:
            async with db_manager.session() as session:
                new_file = UserUploadedFilesCreate(
                    user_id=user_id,
                    name=file_name,
                    hash=file_hash,
                    size=file_size,
                    type=file_type,
                    rag_file_id=file_id,
                    rag_server=self.rag_server,
                    status=status,
                )
                await user_uploaded_files_crud.create(session, obj_in=new_file)
                return new_file
        except Exception as e:
            print(f"Error saving file to database: {e}")
            return None

    async def upload_file(self, *, user_id: str, file_content: bytes, file_name: str):
        """
        上传文件，保存的到用户文件表
        """
        file_size = len(file_content)
        file_hash = hashlib.sha256(file_content).hexdigest()
        file_type, _ = mimetypes.guess_type(file_name)
        result = await self.upload_file_to_rag_service(
            file_content, file_name, file_size, file_hash, file_type
        )
        status = result.status
        rag_file_id = result.rag_file_id
        await self._save_file(
            user_id=user_id,
            file_id=rag_file_id,
            file_name=file_name,
            file_size=file_size,
            file_hash=file_hash,
            file_type=file_type,
            status=status,
        )
        return result

    def delete_files(
        self,
        ids: List[str],
        user_id: str,
    ) -> bool:
        """
        删除文件，首先从rag服务删除，然后在从用户上传文件表删除
        """
        pass

    def retrieve(
        query: str,
        ids: List[str],
        user_id: str,
    ):
        """
        召回文件内容
        """
        pass

    @abstractmethod
    async def upload_file_to_rag_service(
        self,
        *,
        file_content: bytes,
        file_name: str,
        file_size: int,
        file_hash: str,
        file_type: str,
    ) -> FileUploadResult:
        """
        RAG 服务上传文件，返回文件信息
        """
        pass

    @abstractmethod
    async def parse_file(self, file_id: str):
        """
        RAG 服务解析文件
        """
        pass


    @abstractmethod
    async def get_file_status(self, file_id: str) -> dict:
        """
        查询文件状态 上传成功 等待解析 解析完成 解析失败
        """
        pass

    @abstractmethod
    async def delete_file_from_rag_service(self, user_id: str, file_ids: List[str]) -> bool:
        """
        RAG 服务删除文件
        """
        pass

    @abstractmethod
    async def retrieve_from_documents(
        self, query: str, user_id: str, file_ids: list[str]
    ) -> list[dict]:
        """
        召回文件内容
        召回时验证是否当前用户的文件
        """
        pass
