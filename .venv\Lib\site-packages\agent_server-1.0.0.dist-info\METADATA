Metadata-Version: 2.4
Name: agent-server
Version: 1.0.0
Summary: A comprehensive AI agent framework with LangChain, LangGraph, and MCP support
Home-page: https://github.com/yourusername/agent-server
Author: Your Name
Author-email: Elephant <<EMAIL>>
License: MIT
Keywords: ai,agent,langchain,langgraph,mcp,llm
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.11
Description-Content-Type: text/markdown
Requires-Dist: alembic>=1.15.2
Requires-Dist: asyncpg>=0.30.0
Requires-Dist: dotenv>=0.9.9
Requires-Dist: fastapi[standard]>=0.115.11
Requires-Dist: httpx>=0.28.1
Requires-Dist: langchain-community>=0.3.24
Requires-Dist: langchain[openai]>=0.3.25
Requires-Dist: langgraph>=0.5.0
Requires-Dist: langgraph-checkpoint-redis>=0.1.0
Requires-Dist: loguru>=0.7.3
Requires-Dist: mcp>=1.9.0
Requires-Dist: pillow>=11.1.0
Requires-Dist: psycopg2-binary>=2.9.10
Requires-Dist: psycopg[binary,pool]>=3.2.6
Requires-Dist: pylint>=3.3.6
Requires-Dist: redis>=6.1.0
Requires-Dist: sqlalchemy>=2.0.41
Requires-Dist: sqlmodel>=0.0.24
Requires-Dist: pyyaml>=6.0
Requires-Dist: nicegui>=1.4.0
Requires-Dist: bcrypt>=4.0.0
Requires-Dist: psutil>=5.9.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: aiohttp>=3.8.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.7.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=7.4.0; extra == "test"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "test"
Requires-Dist: pytest-cov>=4.1.0; extra == "test"
Dynamic: author
Dynamic: home-page
Dynamic: requires-python

# agent-server

LLM基础后端服务

## 开发

### 安装依赖
```sh
pip install uv

uv sync
```

### 启动服务
```sh
uv run -m main
```

## docker 部署

### 本地构建
```bash
# develop 版本
# 构建镜像 PowerShell
# $env:HTTPS_PROXY="http://127.0.0.1:10888"
docker build -t agent-server/latest .

# 保存镜像 WSL 环境
docker save agent-server/latest | gzip > ./docker/agent_server_latest.tar.gz


# prod 版本
docker build -t agent-server/prod .
docker save agent-server/prod | gzip > ./docker/agent_server_prod.tar.gz
```

### 服务器部署
```bash
# 解压镜像
gunzip -c agent_server_latest.tar.gz > agent_server_latest.tar

# 删除旧的镜像
docker rmi agent-server/latest

# 加载镜像
docker load < agent_server_latest.tar

# 删除旧容器
sudo docker container stop agent-server
sudo docker container rm agent-server

# 运行容器
sudo docker run \
  --add-host=host.docker.internal:host-gateway \
  -e APP_ENV="prod" \
  -e CONFIG_PATH="/app/config/application.yml" \
  -v /data/agent/app/logs:/app/logs \
  -v /data/agent/app/config:/app/config \
  -p 5801:8000 \
  --name agent-server \
  -d agent-server/latest
```

## 数据库

### 备份

```bash
sudo docker exec -t timescaledb pg_dump -U postgres -Fc agent_db > /data/backups/my_database_$(date +%Y%m%d%H%M%S).dump
```

## Todo List

- [x] 配置文件移到镜像外，通过环境变量配置路径
- [x] log 文件目录
- [ ] 多 Agent 集成架构设计
- [ ] 通用异常判断
- [ ] 通用 Response
- [ ] 打包配置

## 运行 框架开发人员
uv pip install -e .[dev]
